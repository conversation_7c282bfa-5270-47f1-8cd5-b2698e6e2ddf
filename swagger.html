<div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Quality" data-tag="Quality" data-is-open="true"><a class="nostyle"><span>Quality</span></a><small><div class="renderedMarkdown"><p>Visual Check Service API for quality management</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get is-open" id="operations-Quality-get_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId_"><div class="opblock-summary opblock-summary-get"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/quality/quality_instant/{qualityInstantId}/subordinates/{subordinateId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/quality<wbr>/quality_instant<wbr>/{qualityInstantId}<wbr>/subordinates<wbr>/{subordinateId}</span></a></span><div class="opblock-summary-description">Get full status details of a specific subordinate</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button aria-label="get ​/api​/v1​/quality​/quality_instant​/{qualityInstantId}​/subordinates​/{subordinateId}" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-description-wrapper"><div class="opblock-description"><div class="renderedMarkdown"><p>Returns detailed status information including history and scheduled status</p></div></div></div><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="table-container"><table class="parameters"><thead><tr><th class="col_header parameters-col_name">Name</th><th class="col_header parameters-col_description">Description</th></tr></thead><tbody><tr data-param-name="qualityInstantId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">qualityInstantId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="qualityInstantId" disabled="" value=""></td></tr><tr data-param-name="subordinateId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">subordinateId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="subordinateId" disabled="" value=""></td></tr></tbody></table></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="get_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="200"><td class="response-col_status">200</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Subordinate details found</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="text/plain">text/plain</option><option value="application/json">application/json</option><option value="text/json">text/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="NDzRQ3c=" aria-selected="true" class="tablinks" data-name="example" id="4NVo8Oc=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="ydi8xyM=" aria-selected="false" class="tablinks" data-name="model" id="plSLM28=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="4NVo8Oc=" data-name="examplePanel" id="NDzRQ3c=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"status_time_summary"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"total_time_formatted"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"total_time_minutes"</span><span>: </span><span style="color: rgb(211, 99, 99);">0</span><span>,
</span><span>      </span><span class="hljs-attr">"status_description"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"status_code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"scheduled_status"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"effectiveEndDate"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.579Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"effectiveStartDate"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.579Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"description"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"status_history"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"endedAt"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.579Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"activatedAt"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.579Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"description"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"current_status"</span><span>: {
</span><span>    </span><span class="hljs-attr">"endedAt"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.579Z"</span><span>,
</span><span>    </span><span class="hljs-attr">"activatedAt"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.579Z"</span><span>,
</span><span>    </span><span class="hljs-attr">"description"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>    </span><span class="hljs-attr">"code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>  },
<span>  </span><span class="hljs-attr">"id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="404"><td class="response-col_status">404</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Subordinate not found</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="500"><td class="response-col_status">500</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Internal server error</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span><span><div class="opblock opblock-post is-open" id="operations-Quality-post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status"><div class="opblock-summary opblock-summary-post"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/quality/quality_instant/{qualityInstantId}/subordinates/{subordinateId}/status"><a class="nostyle"><span>/api<wbr>/v1<wbr>/quality<wbr>/quality_instant<wbr>/{qualityInstantId}<wbr>/subordinates<wbr>/{subordinateId}<wbr>/status</span></a></span><div class="opblock-summary-description">Set a new status for a subordinate</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button aria-label="post ​/api​/v1​/quality​/quality_instant​/{qualityInstantId}​/subordinates​/{subordinateId}​/status" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-description-wrapper"><div class="opblock-description"><div class="renderedMarkdown"><p>Updates the current status of a subordinate and sends event to service bus</p></div></div></div><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="table-container"><table class="parameters"><thead><tr><th class="col_header parameters-col_name">Name</th><th class="col_header parameters-col_description">Description</th></tr></thead><tbody><tr data-param-name="qualityInstantId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">qualityInstantId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="qualityInstantId" disabled="" value=""></td></tr><tr data-param-name="subordinateId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">subordinateId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="subordinateId" disabled="" value=""></td></tr></tbody></table></div></div><div class="opblock-section opblock-section-request-body"><div class="opblock-section-header"><h4 class="opblock-title parameter__name undefined">Request body</h4><label id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status_requests_select"><div class="content-type-wrapper body-param-content-type"><select aria-label="Request content type" class="content-type" id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status_requests_select"><option value="application/json-patch+json">application/json-patch+json</option><option value="application/json">application/json</option><option value="text/json">text/json</option><option value="application/*+json">application/*+json</option></select></div></label></div><div class="opblock-description-wrapper"><div><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="2i/x4ng=" aria-selected="true" class="tablinks" data-name="example" id="6NPogFE=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="cqCFFX4=" aria-selected="false" class="tablinks" data-name="model" id="RKFcrdA=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="6NPogFE=" data-name="examplePanel" id="2i/x4ng=" role="tabpanel" tabindex="0"><div class="highlight-code"><pre class="body-param__example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"statusCode"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status_responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="200"><td class="response-col_status">200</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Status updated successfully</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="text/plain">text/plain</option><option value="application/json">application/json</option><option value="text/json">text/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="1uraKBo=" aria-selected="true" class="tablinks" data-name="example" id="luGvMD8=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="zpbXpsM=" aria-selected="false" class="tablinks" data-name="model" id="PxJaIr0=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="luGvMD8=" data-name="examplePanel" id="1uraKBo=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"message"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="404"><td class="response-col_status">404</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Quality instant or subordinate not found</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="500"><td class="response-col_status">500</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Internal server error</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span><span><div class="opblock opblock-post is-open" id="operations-Quality-post_api_v1_quality_quality_instant__qualityInstantId__subordinates_status_bulk"><div class="opblock-summary opblock-summary-post"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/quality/quality_instant/{qualityInstantId}/subordinates/status/bulk"><a class="nostyle"><span>/api<wbr>/v1<wbr>/quality<wbr>/quality_instant<wbr>/{qualityInstantId}<wbr>/subordinates<wbr>/status<wbr>/bulk</span></a></span><div class="opblock-summary-description">Bulk set a new status for multiple subordinates</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button aria-label="post ​/api​/v1​/quality​/quality_instant​/{qualityInstantId}​/subordinates​/status​/bulk" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-description-wrapper"><div class="opblock-description"><div class="renderedMarkdown"><p>Updates the current status of multiple subordinates in a single quality instant and sends events to service bus</p></div></div></div><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="table-container"><table class="parameters"><thead><tr><th class="col_header parameters-col_name">Name</th><th class="col_header parameters-col_description">Description</th></tr></thead><tbody><tr data-param-name="qualityInstantId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">qualityInstantId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="qualityInstantId" disabled="" value=""></td></tr></tbody></table></div></div><div class="opblock-section opblock-section-request-body"><div class="opblock-section-header"><h4 class="opblock-title parameter__name undefined">Request body</h4><label id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates_status_bulk_requests_select"><div class="content-type-wrapper body-param-content-type"><select aria-label="Request content type" class="content-type" id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates_status_bulk_requests_select"><option value="application/json-patch+json">application/json-patch+json</option><option value="application/json">application/json</option><option value="text/json">text/json</option><option value="application/*+json">application/*+json</option></select></div></label></div><div class="opblock-description-wrapper"><div><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="rW8jjno=" aria-selected="true" class="tablinks" data-name="example" id="a7ZsjZw=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="uxNrMMY=" aria-selected="false" class="tablinks" data-name="model" id="YAuzEZM=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="a7ZsjZw=" data-name="examplePanel" id="rW8jjno=" role="tabpanel" tabindex="0"><div class="highlight-code"><pre class="body-param__example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"statusCode"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"operatorIds"</span><span>: [
</span><span>    </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>  ]
}</code></pre></div></div></div></div></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates_status_bulk_responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="200"><td class="response-col_status">200</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Statuses processed</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="text/plain">text/plain</option><option value="application/json">application/json</option><option value="text/json">text/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="BqRWv5g=" aria-selected="true" class="tablinks" data-name="example" id="5jtRIUU=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="s58U/Dc=" aria-selected="false" class="tablinks" data-name="model" id="RWpEuks=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="5jtRIUU=" data-name="examplePanel" id="BqRWv5g=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"shiftInstantId"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"statusCode"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"totalRequested"</span><span>: </span><span style="color: rgb(211, 99, 99);">0</span><span>,
</span><span>  </span><span class="hljs-attr">"succeeded"</span><span>: </span><span style="color: rgb(211, 99, 99);">0</span><span>,
</span><span>  </span><span class="hljs-attr">"failed"</span><span>: </span><span style="color: rgb(211, 99, 99);">0</span><span>,
</span><span>  </span><span class="hljs-attr">"results"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"operatorId"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"success"</span><span>: </span><span style="color: rgb(252, 194, 140);">true</span><span>,
</span><span>      </span><span class="hljs-attr">"message"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"correlationId"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"timestamp"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.587Z"</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="400"><td class="response-col_status">400</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Bad request</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="404"><td class="response-col_status">404</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Quality instant not found</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="500"><td class="response-col_status">500</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Internal server error</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span><span><div class="opblock opblock-post is-open" id="operations-Quality-post_api_v1_quality_quality_instant__qualityInstantId__status"><div class="opblock-summary opblock-summary-post"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/quality/quality_instant/{qualityInstantId}/status"><a class="nostyle"><span>/api<wbr>/v1<wbr>/quality<wbr>/quality_instant<wbr>/{qualityInstantId}<wbr>/status</span></a></span><div class="opblock-summary-description">Update the overall status of a quality shift</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button aria-label="post ​/api​/v1​/quality​/quality_instant​/{qualityInstantId}​/status" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-description-wrapper"><div class="opblock-description"><div class="renderedMarkdown"><p>Updates the status of a quality instant. Possible status values: INIT, VISUAL_CHECK, STARTED, CLOSED, REVIEW, VALIDATED</p></div></div></div><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="table-container"><table class="parameters"><thead><tr><th class="col_header parameters-col_name">Name</th><th class="col_header parameters-col_description">Description</th></tr></thead><tbody><tr data-param-name="qualityInstantId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">qualityInstantId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="qualityInstantId" disabled="" value=""></td></tr></tbody></table></div></div><div class="opblock-section opblock-section-request-body"><div class="opblock-section-header"><h4 class="opblock-title parameter__name undefined">Request body</h4><label id="post_api_v1_quality_quality_instant__qualityInstantId__status_requests_select"><div class="content-type-wrapper body-param-content-type"><select aria-label="Request content type" class="content-type" id="post_api_v1_quality_quality_instant__qualityInstantId__status_requests_select"><option value="application/json-patch+json">application/json-patch+json</option><option value="application/json">application/json</option><option value="text/json">text/json</option><option value="application/*+json">application/*+json</option></select></div></label></div><div class="opblock-description-wrapper"><div><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="r7bQHL4=" aria-selected="true" class="tablinks" data-name="example" id="oD4T3xA=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="wFfZ9EY=" aria-selected="false" class="tablinks" data-name="model" id="FI/sECA=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="oD4T3xA=" data-name="examplePanel" id="r7bQHL4=" role="tabpanel" tabindex="0"><div class="highlight-code"><pre class="body-param__example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"ShiftStatus"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="post_api_v1_quality_quality_instant__qualityInstantId__status_responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="200"><td class="response-col_status">200</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Quality shift status updated successfully</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="text/plain">text/plain</option><option value="application/json">application/json</option><option value="text/json">text/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="NVgMxko=" aria-selected="true" class="tablinks" data-name="example" id="gAgoyNo=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="pdnD2x8=" aria-selected="false" class="tablinks" data-name="model" id="Q91bCCQ=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="gAgoyNo=" data-name="examplePanel" id="NVgMxko=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"timestamp"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.590Z"</span><span>,
</span><span>  </span><span class="hljs-attr">"data"</span><span>: {
</span><span>    </span><span class="hljs-attr">"next_allowed_statuses"</span><span>: [
</span><span>      </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    ],
<span>    </span><span class="hljs-attr">"shift_date"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>    </span><span class="hljs-attr">"team_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>    </span><span class="hljs-attr">"status_changed_at"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.590Z"</span><span>,
</span><span>    </span><span class="hljs-attr">"current_status"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>    </span><span class="hljs-attr">"previous_status"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>    </span><span class="hljs-attr">"shift_instant_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>  },
<span>  </span><span class="hljs-attr">"message"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"success"</span><span>: </span><span style="color: rgb(252, 194, 140);">true</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="400"><td class="response-col_status">400</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Bad request</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="404"><td class="response-col_status">404</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Quality instant not found</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="500"><td class="response-col_status">500</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Internal server error</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span><span><div class="opblock opblock-post is-open" id="operations-Quality-post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status_manual"><div class="opblock-summary opblock-summary-post"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/quality/quality_instant/{qualityInstantId}/subordinates/{subordinateId}/status/manual"><a class="nostyle"><span>/api<wbr>/v1<wbr>/quality<wbr>/quality_instant<wbr>/{qualityInstantId}<wbr>/subordinates<wbr>/{subordinateId}<wbr>/status<wbr>/manual</span></a></span><div class="opblock-summary-description">Manually apply status interval for a subordinate</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button aria-label="post ​/api​/v1​/quality​/quality_instant​/{qualityInstantId}​/subordinates​/{subordinateId}​/status​/manual" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-description-wrapper"><div class="opblock-description"><div class="renderedMarkdown"><p>Manually inserts a status interval for a subordinate in the specified time range</p></div></div></div><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="table-container"><table class="parameters"><thead><tr><th class="col_header parameters-col_name">Name</th><th class="col_header parameters-col_description">Description</th></tr></thead><tbody><tr data-param-name="qualityInstantId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">qualityInstantId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="qualityInstantId" disabled="" value=""></td></tr><tr data-param-name="subordinateId" data-param-in="path"><td class="parameters-col_name"><div class="parameter__name required">subordinateId<span>&nbsp;*</span></div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(path)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="subordinateId" disabled="" value=""></td></tr></tbody></table></div></div><div class="opblock-section opblock-section-request-body"><div class="opblock-section-header"><h4 class="opblock-title parameter__name undefined">Request body</h4><label id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status_manual_requests_select"><div class="content-type-wrapper body-param-content-type"><select aria-label="Request content type" class="content-type" id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status_manual_requests_select"><option value="application/json-patch+json">application/json-patch+json</option><option value="application/json">application/json</option><option value="text/json">text/json</option><option value="application/*+json">application/*+json</option></select></div></label></div><div class="opblock-description-wrapper"><div><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="+8FhZYA=" aria-selected="true" class="tablinks" data-name="example" id="XgvbQ3E=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="2N+28gU=" aria-selected="false" class="tablinks" data-name="model" id="z3PNLB0=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="XgvbQ3E=" data-name="examplePanel" id="+8FhZYA=" role="tabpanel" tabindex="0"><div class="highlight-code"><pre class="body-param__example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"statusCode"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"endDateTime"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"startDateTime"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="post_api_v1_quality_quality_instant__qualityInstantId__subordinates__subordinateId__status_manual_responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="200"><td class="response-col_status">200</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Manual status interval applied successfully</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="text/plain">text/plain</option><option value="application/json">application/json</option><option value="text/json">text/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="1xpn5G0=" aria-selected="true" class="tablinks" data-name="example" id="4Un8qB8=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="dQRvLxQ=" aria-selected="false" class="tablinks" data-name="model" id="/ElZn7o=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="4Un8qB8=" data-name="examplePanel" id="1xpn5G0=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"message"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"history"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"endedAt"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.593Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"activatedAt"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.593Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"description"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"intervals_added"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"end"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.593Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"start"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.593Z"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"applied_status_code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"operator_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"shift_instant_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="400"><td class="response-col_status">400</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Bad request</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="404"><td class="response-col_status">404</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Quality instant or subordinate not found</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="500"><td class="response-col_status">500</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Internal server error</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span><span><div class="opblock opblock-get is-open" id="operations-Quality-get_api_v1_quality_shift_types"><div class="opblock-summary opblock-summary-get"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/quality/shift-types"><a class="nostyle"><span>/api<wbr>/v1<wbr>/quality<wbr>/shift-types</span></a></span><div class="opblock-summary-description">Get shift types for a date</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button aria-label="get ​/api​/v1​/quality​/shift-types" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-description-wrapper"><div class="opblock-description"><div class="renderedMarkdown"><p>Returns all shift types for the authenticated quality superior on the specified date (yyyy-MM-dd).</p></div></div></div><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="table-container"><table class="parameters"><thead><tr><th class="col_header parameters-col_name">Name</th><th class="col_header parameters-col_description">Description</th></tr></thead><tbody><tr data-param-name="date" data-param-in="query"><td class="parameters-col_name"><div class="parameter__name">date</div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(query)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="date" disabled="" value=""></td></tr></tbody></table></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="get_api_v1_quality_shift-types_responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="200"><td class="response-col_status">200</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Successfully retrieved shift types</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="text/plain">text/plain</option><option value="application/json">application/json</option><option value="text/json">text/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="tvxTiUc=" aria-selected="true" class="tablinks" data-name="example" id="KCpIfL8=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="+aopTyo=" aria-selected="false" class="tablinks" data-name="model" id="MhnpTxs=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="KCpIfL8=" data-name="examplePanel" id="tvxTiUc=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"count"</span><span>: </span><span style="color: rgb(211, 99, 99);">0</span><span>,
</span><span>  </span><span class="hljs-attr">"shift_types"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"designation"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"shift_instant_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"shift_type"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"date"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"superior_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="400"><td class="response-col_status">400</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Bad request</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="401"><td class="response-col_status">401</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Unauthorized</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="500"><td class="response-col_status">500</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Internal server error</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span><span><div class="opblock opblock-get is-open" id="operations-Quality-get_api_v1_quality_status_monitoring"><div class="opblock-summary opblock-summary-get"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/quality/status-monitoring"><a class="nostyle"><span>/api<wbr>/v1<wbr>/quality<wbr>/status-monitoring</span></a></span><div class="opblock-summary-description">Get quality subordinates current status</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button aria-label="get ​/api​/v1​/quality​/status-monitoring" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-description-wrapper"><div class="opblock-description"><div class="renderedMarkdown"><p>Returns all subordinates for a quality supervisor with their current status and quality instant details for a date range. If only 'date' is provided, it will return data for that single date. If both 'startDate' and 'endDate' are provided, it will return data for the date range.</p></div></div></div><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="table-container"><table class="parameters"><thead><tr><th class="col_header parameters-col_name">Name</th><th class="col_header parameters-col_description">Description</th></tr></thead><tbody><tr data-param-name="startDate" data-param-in="query"><td class="parameters-col_name"><div class="parameter__name">startDate</div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(query)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="startDate" disabled="" value=""></td></tr><tr data-param-name="qualityInstantId" data-param-in="query"><td class="parameters-col_name"><div class="parameter__name">qualityInstantId</div><div class="parameter__type">string</div><div class="parameter__deprecated"></div><div class="parameter__in">(query)</div></td><td class="parameters-col_description"><input type="text" class="" title="" placeholder="qualityInstantId" disabled="" value=""></td></tr><tr data-param-name="mfg" data-param-in="query"><td class="parameters-col_name"><div class="parameter__name">mfg</div><div class="parameter__type">boolean</div><div class="parameter__deprecated"></div><div class="parameter__in">(query)</div></td><td class="parameters-col_description"><select class="" disabled=""><option value="">--</option><option value="true">true</option><option value="false">false</option></select></td></tr></tbody></table></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="get_api_v1_quality_status-monitoring_responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="200"><td class="response-col_status">200</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Successfully retrieved quality subordinates current status</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="text/plain">text/plain</option><option value="application/json">application/json</option><option value="text/json">text/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="qMLgKgQ=" aria-selected="true" class="tablinks" data-name="example" id="ERrL6js=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="d+iPmSw=" aria-selected="false" class="tablinks" data-name="model" id="qAjAU9A=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="ERrL6js=" data-name="examplePanel" id="qMLgKgQ=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"teams_under_review"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"review_date"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.598Z"</span><span>,
</span><span>      </span><span class="hljs-attr">"comment"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"team_name"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"team_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"status_data"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"isMFG"</span><span>: </span><span style="color: rgb(252, 194, 140);">true</span><span>,
</span><span>      </span><span class="hljs-attr">"dailyRecords"</span><span>: [
</span>        {
<span>          </span><span class="hljs-attr">"statusCodes"</span><span>: [
</span>            {
<span>              </span><span class="hljs-attr">"startDateTime"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2025-10-05T17:51:34.598Z"</span><span>,
</span><span>              </span><span class="hljs-attr">"code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>            }
          ],
<span>          </span><span class="hljs-attr">"date"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>        }
      ],
<span>      </span><span class="hljs-attr">"team_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"function"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"role"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"last_name"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"first_name"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>      </span><span class="hljs-attr">"legacy_id"</span><span>: </span><span style="color: rgb(211, 99, 99);">0</span><span>,
</span><span>      </span><span class="hljs-attr">"id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>    }
  ],
<span>  </span><span class="hljs-attr">"shift_end"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"shift_start"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"shift_status"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"shift_leader_instant_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"shift_type"</span><span>: </span><span style="color: rgb(162, 252, 162);">"NULL"</span><span>,
</span><span>  </span><span class="hljs-attr">"shift_instant_id"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>,
</span><span>  </span><span class="hljs-attr">"last_date"</span><span>: </span><span style="color: rgb(162, 252, 162);">"string"</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="400"><td class="response-col_status">400</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Bad request</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="404"><td class="response-col_status">404</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Quality supervisor not found</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="500"><td class="response-col_status">500</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Internal server error</p></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span></div> </div></div>